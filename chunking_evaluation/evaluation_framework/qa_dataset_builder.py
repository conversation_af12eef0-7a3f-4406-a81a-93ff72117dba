"""
中文合成评估模块

这个模块提供了基于AI生成问题和答案的中文合成评估功能。
它可以自动从用户提供的中文语料库中生成问题-答案对，用于评估分块策略。
"""

from typing import List
import os
import json
import random

from .base_evaluation import BaseEvaluation
from .llm_config import get_openai_client, create_chat_completion, create_embeddings, CHAT_MODEL, EMBEDDING_MODEL, EMBEDDING_DIMENSIONS

import pandas as pd
import numpy as np
from importlib import resources

class SyntheticEvaluationChinese(BaseEvaluation):
    """
    中文合成评估类
    
    这个类继承自BaseEvaluation，专门用于基于AI生成的中文问题-答案对进行评估。
    
    主要功能：
    - 自动从中文语料库生成问题和参考答案
    - 提供质量过滤功能，移除低质量的问题-答案对
    - 支持去重功能，避免生成重复的问题
    """
    
    def __init__(self, corpora_paths: List[str], queries_csv_path: str, chroma_db_path:str = None):
        """
        初始化中文合成评估类
        
        参数:
            corpora_paths (List[str]): 语料库文件路径列表
            queries_csv_path (str): 保存生成问题的CSV文件路径
            chroma_db_path (str, 可选): ChromaDB数据库路径
        """
        # 调用父类构造函数
        super().__init__(questions_csv_path=queries_csv_path, chroma_db_path=chroma_db_path)
        
        # 存储语料库路径列表
        self.corpora_paths = corpora_paths
        # 存储问题CSV文件路径
        self.questions_csv_path = queries_csv_path
        # 初始化OpenAI客户端
        self.client = get_openai_client()

        # 标准化数据集根目录（用于相对路径存取）
        from pathlib import Path
        self._datasets_dir = (Path(__file__).parent / 'general_evaluation_data' / 'datasets').resolve()

        # 初始化合成问题数据框
        self.synth_questions_df = None

        # 加载用于生成问题的中文提示模板
        with resources.as_file(resources.files('chunking_evaluation.evaluation_framework') / 'prompts') as prompt_path:
            # 系统提示
            with open(os.path.join(prompt_path, 'question_maker_system.txt'), 'r', encoding='utf-8') as f:
                self.question_maker_system_prompt = f.read()
            
            # 用户提示
            with open(os.path.join(prompt_path, 'question_maker_user.txt'), 'r', encoding='utf-8') as f:
                self.question_maker_user_prompt = f.read()

    def _save_questions_df(self):
        """
        保存合成问题数据框到CSV文件
        
        这个私有方法将当前的合成问题数据框保存到指定的CSV文件中。
        """
        # 确保使用UTF-8编码并添加BOM以避免乱码
        self.synth_questions_df.to_csv(
            self.questions_csv_path, 
            index=False, 
            encoding='utf-8-sig',  # 使用utf-8-sig添加BOM
            escapechar='\\',       # 转义字符
            quoting=1              # 引用所有字段
        )

    def _extract_question_and_references(self, corpus, document_length=4000, prev_questions=[]):
        """
        从语料库中提取问题和参考答案
        
        参数:
            corpus (str): 完整的语料库文本
            document_length (int): 用于生成问题的文档片段长度，默认4000字符
            prev_questions (list): 之前生成的问题列表，用于避免重复
            
        返回:
            tuple: (question, references)
                - question: 生成的问题文本
                - references: 参考答案列表
        """
        # 如果语料库太长，随机选择一个片段
        if len(corpus) > document_length:
            start_index = random.randint(0, len(corpus) - document_length)
            document = corpus[start_index : start_index + document_length]
        else:
            start_index = 0
            document = corpus
        
        # 准备之前问题的字符串，用于避免生成重复问题
        if prev_questions is not None:
            if len(prev_questions) > 20:
                # 如果之前的问题太多，随机选择20个作为样本
                questions_sample = random.sample(prev_questions, 20)
                prev_questions_str = '\n'.join(questions_sample)
            else:
                prev_questions_str = '\n'.join(prev_questions)
        else:
            prev_questions_str = ""

        # 调用OpenAI API生成问题和参考答案
        try:
            completion = create_chat_completion(
                messages=[
                    {"role": "system", "content": self.question_maker_system_prompt},
                    {"role": "user", "content": self.question_maker_user_prompt.replace("{document}", document).replace("{prev_questions_str}", prev_questions_str)}
                ],
                response_format={ "type": "json_object" }  # 要求返回JSON格式
            )
        except Exception as e:
            error_str = str(e)
            if "data_inspection_failed" in error_str or "inappropriate content" in error_str:
                # 如果是内容审核失败，抛出特定异常
                raise Exception(f"内容审核失败：{e}")
            else:
                # 其他API错误，直接抛出
                raise e
        
        # 解析AI返回的JSON响应
        json_response = json.loads(completion.choices[0].message.content)
        
        # 提取问题和参考答案
        try:
            text_references = json_response['references']
        except KeyError:
            raise ValueError("响应中不包含'references'字段。")
        try:
            question = json_response['question']
        except KeyError:
            raise ValueError("响应中不包含'question'字段。")

        # 处理参考答案
        references = []
        for reference in text_references:
            if isinstance(reference, dict):
                content_value = reference.get('content', '')
            else:
                content_value = str(reference)
            if not content_value:
                continue
            references.append(content_value)

        return question, references

    def _generate_corpus_questions(self, corpus_id, n=5):
        """
        为指定语料库生成问题和参考答案
        
        这个私有方法为单个语料库生成指定数量的问题-答案对。
        它会重试失败的生成尝试，直到成功生成所需数量的问题。
        
        参数:
            corpus_id (str): 语料库文件路径
            n (int): 要生成的问题数量，默认5个
        """
        # 读取语料库文件内容（支持传入绝对路径或相对ID）
        from pathlib import Path
        p_in = Path(str(corpus_id))
        if p_in.is_absolute():
            corpus_path = p_in.resolve()
        else:
            corpus_path = (self._datasets_dir / p_in).resolve()

        # 统一生成相对ID（写入CSV时使用）
        try:
            relative_id = str(corpus_path.relative_to(self._datasets_dir))
        except Exception:
            relative_id = corpus_path.name

        with open(corpus_path, 'r', encoding='utf-8') as file:
            corpus = file.read()

        # 检查当前语料库已生成的问题数量
        existing_questions = self.synth_questions_df[self.synth_questions_df['corpus_id'] == relative_id]
        existing_count = len(existing_questions)
        
        if existing_count >= n:
            print(f"语料库 {relative_id} 已生成 {existing_count} 个问题，已达到目标数量 {n}，跳过生成")
            return
        
        # 计算还需要生成的问题数量
        remaining_count = n - existing_count
        print(f"语料库 {relative_id} 已生成 {existing_count} 个问题，还需要生成 {remaining_count} 个问题")

        i = 0  # 成功生成的问题计数器
        max_retries_per_question = 5  # 每个问题的最大重试次数
        content_filter_retries = 0  # 内容审核失败的重试次数
        max_content_filter_retries = 3  # 内容审核失败的最大重试次数
        
        # 循环直到生成足够数量的问题
        while i < remaining_count:
            # 内层循环用于重试失败的生成尝试
            retry_count = 0
            while retry_count < max_retries_per_question:
                try:
                    print(f"正在尝试生成第{existing_count + i + 1}个问题（第{retry_count + 1}次尝试）")
                    
                    # 获取当前语料库已生成的问题列表，用于避免重复
                    questions_list = self.synth_questions_df[self.synth_questions_df['corpus_id'] == relative_id]['question'].tolist()

                    question, references = self._extract_question_and_references(corpus, 4000, questions_list)
                    
                    # 限制参考答案数量，避免过于复杂的问题
                    if len(references) > 3:
                        raise ValueError("参考答案数量超过3个。")
                    
                    # 将参考答案转换为字典格式
                    references = [{'content': ref} for ref in references]
                    
                    # 创建新问题记录
                    new_question = {
                        'question': question,
                        'references': json.dumps(references, ensure_ascii=False, indent=None),  # 确保中文不被转义
                        'corpus_id': relative_id  # 存相对路径
                    }

                    # 将新问题添加到数据框中
                    new_df = pd.DataFrame([new_question])
                    self.synth_questions_df = pd.concat([self.synth_questions_df, new_df], ignore_index=True)
                    
                    # 立即保存到文件，防止数据丢失
                    self._save_questions_df()

                    print(f"成功生成第{existing_count + i + 1}个问题")
                    break  # 成功生成，跳出重试循环
                    
                except ValueError as e:
                    print(f"生成过程中发生错误：{e}")
                    retry_count += 1
                    continue  # 重试生成
                    
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误：{e}")
                    retry_count += 1
                    continue  # 重试生成
                    
                except Exception as e:
                    error_str = str(e)
                    if "内容审核失败" in error_str or "data_inspection_failed" in error_str or "inappropriate content" in error_str:
                        print(f"内容审核失败（第{content_filter_retries + 1}次）：{e}")
                        content_filter_retries += 1
                        
                        if content_filter_retries >= max_content_filter_retries:
                            print(f"内容审核失败次数过多，跳过当前语料库：{relative_id}")
                            return  # 跳过整个语料库
                        
                        # 尝试使用不同的文档片段
                        print("尝试使用不同的文档片段...")
                        retry_count += 1
                        continue
                    else:
                        print(f"未知错误：{e}")
                        retry_count += 1
                        continue
            
            # 如果重试次数达到上限，跳过当前问题
            if retry_count >= max_retries_per_question:
                print(f"生成第{existing_count + i + 1}个问题失败，已达到最大重试次数，跳过")
                # 可以选择继续生成下一个问题，或者直接返回
                # 这里选择继续，避免因为单个问题失败而影响整个语料库
            
            i += 1  # 成功生成一个问题，计数器加1

    def _get_synth_questions_df(self):
        """
        获取或创建合成问题数据框
        
        这个私有方法尝试从CSV文件加载现有的问题数据，
        如果文件不存在则创建一个空的数据框。
        
        返回:
            pandas.DataFrame: 包含问题数据的数据框
        """
        if os.path.exists(self.questions_csv_path):
            # 如果CSV文件存在，加载现有数据，并统一标准化 corpus_id 为相对路径
            synth_questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
            from pathlib import Path
            datasets_dir = self._datasets_dir

            def _normalize_corpus_id(v):
                try:
                    p = Path(str(v))
                    if p.is_absolute():
                        try:
                            return str(p.resolve().relative_to(datasets_dir))
                        except Exception:
                            return p.name
                    v_str = str(v)
                    marker = str(datasets_dir) + os.sep
                    idx = v_str.find(marker)
                    if idx != -1:
                        return v_str[idx + len(marker):]
                    return v_str
                except Exception:
                    return str(v)

            if 'corpus_id' in synth_questions_df.columns:
                synth_questions_df['corpus_id'] = synth_questions_df['corpus_id'].apply(_normalize_corpus_id)
        else:
            # 如果文件不存在，创建空的数据框
            synth_questions_df = pd.DataFrame(columns=['question', 'references', 'corpus_id'])
        return synth_questions_df

    def generate_queries_and_excerpts(self, num_rounds: int = -1, queries_per_corpus: int = 5):
        """
        生成问题和摘录的主要方法
        
        此方法为所有语料库生成问题-答案对。
        可控制生成轮数与每个语料库的问题数量。
        
        参数:
            num_rounds (int): 生成轮数，-1表示无限轮数，默认-1
            queries_per_corpus (int): 每个语料库每轮生成的问题数量，默认5个
        """
        # 获取或创建合成问题数据框
        self.synth_questions_df = self._get_synth_questions_df()

        rounds = 0  # 轮数计数器
        # 循环生成问题，直到达到指定轮数
        while num_rounds == -1 or rounds < num_rounds:
            # 为每个语料库生成问题
            for corpus_id in self.corpora_paths:
                self._generate_corpus_questions(corpus_id, n=queries_per_corpus)
            rounds += 1  # 完成一轮，计数器加1

    def _get_sim(self, target, references):
        """
        计算目标文本与参考答案之间的余弦相似度
        
        这个方法使用嵌入模型计算问题与其参考答案之间的语义相似度。
        用于质量过滤，移除相似度过低的问题-答案对。
        
        参数:
            target (str): 目标文本（通常是问题）
            references (list): 参考答案文本列表
            
        返回:
            list: 目标文本与每个参考答案的余弦相似度分数列表
        """
        # 调用OpenAI嵌入API，获取目标文本和所有参考答案的嵌入向量
        response = create_embeddings([target]+references)
        
        # 获取目标文本的嵌入向量（第一个元素）
        nparray1 = np.array(response.data[0].embedding)

        full_sim = []  # 存储所有相似度分数
        # 计算目标文本与每个参考答案的余弦相似度
        for i in range(1, len(response.data)):
            nparray2 = np.array(response.data[i].embedding)
            # 计算余弦相似度：两个向量的点积除以它们的模长乘积
            cosine_similarity = np.dot(nparray1, nparray2) / (np.linalg.norm(nparray1) * np.linalg.norm(nparray2))
            full_sim.append(cosine_similarity)
    
        return full_sim

    def _corpus_filter_poor_highlights(self, corpus_id, synth_questions_df, threshold):
        """
        为指定语料库过滤低质量的高亮内容
        
        这个私有方法处理单个语料库的问题-答案对，移除问题与参考答案之间
        语义相似度过低的条目。它计算每个问题与其所有参考答案的相似度，
        并使用最低相似度分数作为质量指标。
        
        参数:
            corpus_id (str): 要处理的语料库ID
            synth_questions_df (pandas.DataFrame): 包含所有问题的数据框
            threshold (float): 相似度阈值，低于此值的问题将被移除
        """
        # 筛选出属于指定语料库的问题
        corpus_questions_df = synth_questions_df[synth_questions_df['corpus_id'] == corpus_id]

        def edit_row(row):
            """
            为每行数据计算最差参考答案分数
            
            这个内部函数计算问题与其所有参考答案的相似度，
            并记录最低的相似度分数作为质量指标。
            """
            question = row['question']  # 获取问题文本
            # 提取所有参考答案的内容
            references = [ref['content'] for ref in row['references']]
            # 计算问题与所有参考答案的相似度分数
            similarity_scores = self._get_sim(question, references)
            # 取最低分数作为该问题的质量指标
            worst_ref_score = min(similarity_scores)
            row['worst_ref_score'] = worst_ref_score
            return row

        # 对每一行应用评分函数
        corpus_questions_df = corpus_questions_df.apply(edit_row, axis=1)

        # 记录过滤前的问题数量
        count_before = len(corpus_questions_df)

        # 过滤掉质量分数低于阈值的问题
        corpus_questions_df = corpus_questions_df[corpus_questions_df['worst_ref_score'] >= threshold]
        # 删除临时添加的评分列
        corpus_questions_df = corpus_questions_df.drop(columns=['worst_ref_score'])

        # 记录过滤后的问题数量
        count_after = len(corpus_questions_df)

        print(f"语料库：{corpus_id} - 移除了 {count_before - count_after} 个低质量问题。")

        # 将参考答案重新序列化为JSON格式
        corpus_questions_df['references'] = corpus_questions_df['references'].apply(lambda x: json.dumps(x, ensure_ascii=False))

        # 重新构建完整的问题数据框
        # 首先加载现有的完整数据
        full_questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
        # 移除当前语料库的旧数据
        full_questions_df = full_questions_df[full_questions_df['corpus_id'] != corpus_id]

        # 添加过滤后的当前语料库数据
        full_questions_df = pd.concat([full_questions_df, corpus_questions_df], ignore_index=True)
        
        # 清理可能存在的临时列
        for col in ['fixed', 'worst_ref_score', 'diff_score']:
            if col in full_questions_df.columns:
                full_questions_df = full_questions_df.drop(columns=col)

        # 保存更新后的数据到文件
        full_questions_df.to_csv(self.questions_csv_path, index=False, encoding='utf-8-sig')

    def filter_poor_excerpts(self, threshold=0.36, corpora_subset=[]):
        """
        过滤低质量的摘录
        
        这个方法移除问题与其参考答案之间语义相似度过低的问题-答案对。
        通过设置相似度阈值来确保生成的问题-答案对具有足够的相关性。
        
        参数:
            threshold (float): 相似度阈值，默认0.36。低于此值的问题-答案对将被移除
            corpora_subset (list): 要处理的语料库子集，空列表表示处理所有语料库
        """
        # 检查问题CSV文件是否存在
        if os.path.exists(self.questions_csv_path):
            # 加载现有的问题数据
            synth_questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
            if len(synth_questions_df) > 0:
                # 将references列从JSON字符串转换为Python对象
                synth_questions_df['references'] = synth_questions_df['references'].apply(json.loads)
                # 标准化 corpus_id 为相对路径
                from pathlib import Path
                datasets_dir = self._datasets_dir
                def _norm(v):
                    try:
                        p = Path(str(v))
                        if p.is_absolute():
                            try:
                                return str(p.resolve().relative_to(datasets_dir))
                            except Exception:
                                return p.name
                        v_str = str(v)
                        marker = str(datasets_dir) + os.sep
                        idx = v_str.find(marker)
                        if idx != -1:
                            return v_str[idx + len(marker):]
                        return v_str
                    except Exception:
                        return str(v)
                synth_questions_df['corpus_id'] = synth_questions_df['corpus_id'].apply(_norm)
                
                # 获取所有唯一的语料库ID
                corpus_list = synth_questions_df['corpus_id'].unique().tolist()
                
                # 如果指定了语料库子集，只处理子集中的语料库
                if corpora_subset:
                    corpus_list = [c for c in corpus_list if c in corpora_subset]
                
                # 对每个语料库应用质量过滤
                for corpus_id in corpus_list:
                    self._corpus_filter_poor_highlights(corpus_id, synth_questions_df, threshold)

    def _corpus_filter_duplicates(self, corpus_id, synth_questions_df, threshold):
        """
        为指定语料库过滤重复的问题
        
        这个私有方法使用语义相似度来识别和移除重复或近似重复的问题。
        它首先移除完全相同的问题，然后使用嵌入向量计算语义相似度，
        移除相似度超过阈值的问题对中的后者。
        
        参数:
            corpus_id (str): 要处理的语料库ID
            synth_questions_df (pandas.DataFrame): 包含所有问题的数据框
            threshold (float): 相似度阈值，高于此值的问题对将被视为重复
        """
        # 筛选出属于指定语料库的问题，并创建副本以避免修改原数据
        corpus_questions_df = synth_questions_df[synth_questions_df['corpus_id'] == corpus_id].copy()

        # 记录过滤前的问题数量
        count_before = len(corpus_questions_df)

        # 首先移除完全相同的问题（基于文本完全匹配）
        corpus_questions_df.drop_duplicates(subset='question', keep='first', inplace=True)

        # 提取所有问题文本用于嵌入计算
        questions = corpus_questions_df['question'].tolist()

        # 使用OpenAI API获取所有问题的嵌入向量
        response = create_embeddings(questions)

        # 将嵌入向量转换为numpy矩阵
        embeddings_matrix = np.array([data.embedding for data in response.data])

        # 计算所有问题对之间的点积（相似度）矩阵
        dot_product_matrix = np.dot(embeddings_matrix, embeddings_matrix.T)

        # 创建包含索引对和相似度的元组列表
        similarity_pairs = [(i, j, dot_product_matrix[i][j]) for i in range(len(dot_product_matrix)) for j in range(i+1, len(dot_product_matrix))]

        # 按相似度降序排列
        similarity_pairs.sort(key=lambda x: x[2], reverse=True)

        # 提取相似度分数数组（用于调试或分析）
        similarity_scores = np.array([x[2] for x in similarity_pairs])

        # 计算每个问题与其他问题的最高相似度（排除自身）
        most_similars = (dot_product_matrix - np.eye(dot_product_matrix.shape[0])).max(axis=1)

        def filter_vectors(sim_matrix, threshold):
            """
            基于相似度阈值过滤向量的内部函数
            
            这个函数实现贪心算法来移除相似的向量。对于每对相似度超过阈值的向量，
            保留索引较小的向量，移除索引较大的向量。
            
            参数:
                sim_matrix (numpy.ndarray): 相似度矩阵
                threshold (float): 相似度阈值
                
            返回:
                numpy.ndarray: 布尔数组，True表示保留该向量
            """
            n = sim_matrix.shape[0]  # 向量数量
            remaining = np.ones(n, dtype=bool)  # 初始化所有向量为保留状态

            for i in range(n):
                if remaining[i] == 1:  # 只检查仍然保留的向量
                    for j in range(i+1, n):
                        # 如果两个向量都还保留且相似度超过阈值
                        if remaining[j] == 1 and sim_matrix[i, j] > threshold:
                            remaining[j] = 0  # 移除向量j，因为它与向量i过于相似
            
            return remaining

        # 应用过滤函数获取要保留的行
        rows_to_keep = filter_vectors(dot_product_matrix, threshold)

        # 根据过滤结果筛选问题数据框
        corpus_questions_df = corpus_questions_df[rows_to_keep]

        # 记录过滤后的问题数量
        count_after = len(corpus_questions_df)

        print(f"语料库：{corpus_id} - 移除了 {count_before - count_after} 个重复问题。")

        # 将参考答案重新序列化为JSON格式
        corpus_questions_df['references'] = corpus_questions_df['references'].apply(lambda x: json.dumps(x, ensure_ascii=False))

        # 重新构建完整的问题数据框
        # 首先加载现有的完整数据
        full_questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
        # 移除当前语料库的旧数据
        full_questions_df = full_questions_df[full_questions_df['corpus_id'] != corpus_id]

        # 添加过滤后的当前语料库数据
        full_questions_df = pd.concat([full_questions_df, corpus_questions_df], ignore_index=True)
        
        # 清理可能存在的临时列
        for col in ['fixed', 'worst_ref_score', 'diff_score']:
            if col in full_questions_df.columns:
                full_questions_df = full_questions_df.drop(columns=col)

        # 保存更新后的数据到文件
        full_questions_df.to_csv(self.questions_csv_path, index=False, encoding='utf-8-sig')

    def filter_duplicates(self, threshold=0.78, corpora_subset=[]):
        """
        过滤重复的问题
        
        这个方法移除语义上过于相似的问题，确保问题集的多样性。
        使用嵌入向量的余弦相似度来识别重复或近似重复的问题。
        
        参数:
            threshold (float): 相似度阈值，默认0.78。高于此值的问题对将被视为重复
            corpora_subset (list): 要处理的语料库子集，空列表表示处理所有语料库
        """
        # 检查问题CSV文件是否存在
        if os.path.exists(self.questions_csv_path):
            # 加载现有的问题数据
            synth_questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
            if len(synth_questions_df) > 0:
                # 将references列从JSON字符串转换为Python对象
                synth_questions_df['references'] = synth_questions_df['references'].apply(json.loads)
                # 标准化 corpus_id 为相对路径
                from pathlib import Path
                datasets_dir = self._datasets_dir
                def _norm(v):
                    try:
                        p = Path(str(v))
                        if p.is_absolute():
                            try:
                                return str(p.resolve().relative_to(datasets_dir))
                            except Exception:
                                return p.name
                        v_str = str(v)
                        marker = str(datasets_dir) + os.sep
                        idx = v_str.find(marker)
                        if idx != -1:
                            return v_str[idx + len(marker):]
                        return v_str
                    except Exception:
                        return str(v)
                synth_questions_df['corpus_id'] = synth_questions_df['corpus_id'].apply(_norm)
                
                # 获取所有唯一的语料库ID
                corpus_list = synth_questions_df['corpus_id'].unique().tolist()
                
                # 如果指定了语料库子集，只处理子集中的语料库
                if corpora_subset:
                    corpus_list = [c for c in corpus_list if c in corpora_subset]
                
                # 对每个语料库应用去重过滤
                for corpus_id in corpus_list:
                    self._corpus_filter_duplicates(corpus_id, synth_questions_df, threshold)

    def question_ref_filter(self):
        """
        问题参考答案过滤器
        
        这个方法重新加载合成问题数据框，通常用于在过滤操作后刷新数据。
        """
        self.synth_questions_df = self._get_synth_questions_df() 
