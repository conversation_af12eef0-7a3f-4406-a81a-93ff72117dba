#!/usr/bin/env python3
"""
调试脚本：重现 'int' object has no attribute 'get' 错误
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from metrics.evaluation_metrics import ComprehensiveEvaluator

from chunking_evaluation.evaluation_framework.base_evaluation import BaseEvaluation
from chunking_evaluation.chunking import FixedTokenChunker
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function

# 导入检索器
from retrieval import DenseRetriever

class ChineseEvaluation(BaseEvaluation):
    """简化的中文评估类用于调试"""

    def __init__(self, questions_csv_path: str, chroma_db_path: str = None):
        # 构建语料库路径映射
        corpora_id_paths = self._build_corpora_paths()
        # 调用父类构造函数
        super().__init__(questions_csv_path, chroma_db_path=chroma_db_path, corpora_id_paths=corpora_id_paths)

    def _build_corpora_paths(self):
        """构建语料库路径映射"""
        corpora_paths = {}
        current_dir = Path(__file__).parent
        datasets_dir = current_dir / "chunking_evaluation" / "evaluation_framework" / "general_evaluation_data" / "datasets"
        
        if not datasets_dir.exists():
            print(f"警告：datasets目录不存在: {datasets_dir}")
            return corpora_paths

        for subdir in datasets_dir.iterdir():
            if subdir.is_dir():
                for file in subdir.iterdir():
                    if file.suffix == '.txt':
                        try:
                            relative_path = str(file.relative_to(datasets_dir))
                        except Exception:
                            relative_path = file.name
                        corpora_paths[relative_path] = str(file.absolute())

        return corpora_paths

def debug_single_evaluation():
    """调试单个评估配置"""
    print("🔍 开始调试评估过程...")
    
    # 数据集路径配置
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/questions.csv"
    base_chroma_db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/questions_db"
    
    # 检查数据集是否存在
    if not os.path.exists(questions_csv_path):
        print(f"❌ 错误：找不到中文数据集文件: {questions_csv_path}")
        return
    
    try:
        # 创建embedding函数
        print("📝 创建embedding函数...")
        embedding_function = get_openai_embedding_function()
        print("✅ embedding函数创建成功")
        
        # 创建分块器
        print("📝 创建分块器...")
        chunker = FixedTokenChunker(chunk_size=100, chunk_overlap=20)
        print("✅ 分块器创建成功")
        
        # 生成数据库路径
        chunker_db_name = "FixedTokenChunker_chunk_overlap20_chunk_size100"
        specific_db_path = str(Path(base_chroma_db_path) / chunker_db_name)
        print(f"📝 数据库路径: {specific_db_path}")
        
        # 创建评估器
        print("📝 创建评估器...")
        evaluator = ChineseEvaluation(questions_csv_path, specific_db_path)
        print("✅ 评估器创建成功")
        
        # 创建稠密检索器实例
        print("📝 创建稠密检索器...")
        dense_retriever = DenseRetriever(
            embedding_function=embedding_function,
            collection_name='dense_retrieval'
        )
        print("✅ 稠密检索器创建成功")
        
        # 测试检索器的get_info方法
        print("📝 测试检索器get_info方法...")
        retriever_info = dense_retriever.get_info()
        print(f"✅ 检索器信息类型: {type(retriever_info)}")
        print(f"✅ 检索器信息内容: {retriever_info}")
        
        # 运行评估
        print("📝 运行评估...")
        result = evaluator.run(
            chunker=chunker,
            embedding_function=embedding_function,
            retrieve=3,
            db_to_save_chunks=specific_db_path,
            include_debug_info=True,
            retriever=dense_retriever
        )
        
        print(f"✅ 评估结果类型: {type(result)}")
        print(f"✅ 评估结果内容: {result}")
        
        # 测试result.get()方法
        print("📝 测试result.get()方法...")
        debug_info = result.get('debug_info', {})
        print(f"✅ debug_info获取成功: {type(debug_info)}")
        
        # 计算信息检索指标
        print("📝 计算信息检索指标...")
        ir_evaluator = ComprehensiveEvaluator()
        ir_metrics = calculate_ir_metrics(result, evaluator.questions_df, ir_evaluator)
        print(f"✅ 信息检索指标计算成功: {type(ir_metrics)}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        print("📋 详细错误信息:")
        traceback.print_exc()

def calculate_ir_metrics(original_results, questions_df, ir_evaluator):
    """简化的信息检索指标计算函数"""
    print(f"📝 original_results类型: {type(original_results)}")
    print(f"📝 original_results内容: {original_results}")
    
    debug_info = original_results.get('debug_info', {})
    questions = debug_info.get('questions', [])
    
    if not questions:
        return {
            'metrics': {'precision': {'mean': 0}, 'recall': {'mean': 0}, 'f1': {'mean': 0}},
            'question_metrics': []
        }
    
    return {'metrics': {'precision': {'mean': 0.5}, 'recall': {'mean': 0.5}, 'f1': {'mean': 0.5}}}

if __name__ == "__main__":
    debug_single_evaluation()
